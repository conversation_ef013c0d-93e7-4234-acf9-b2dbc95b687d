const express = require('express');
const { getShard, connectShards } = require('./db');

const app = express();

app.use(express.json());

app.get('/user/:id', async (req, res) => {
  const userId = parseInt(req.params.id);

  const shardDb = getShard(userId);

  if (!shardDb) return res.status(500).send("Shards not connected");

  const user = await shardDb.collection('users').findOne({ userId });

  if (!user) return res.status(404).send("User not found");

  res.json(user);

});

app.post("/user", async (req, res) => {
  const {userId, name } = req.body;

  const shardDb = getShard(userId);

  if (!shardDb) return res.status(500).send("Shards not connected");

  await shardDb.collection('users').insertOne({ userId, name });

  res.json({ message: "User created" });
})

// Test basic Express functionality first
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

(async () => {
  await connectShards();
  app.listen(3000, () => {
    console.log("Server started on port 3000");
  });
})();