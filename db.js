const { MongoClient } =  require('mongodb');

const mongoUri = 'mongodb://127.0.0.1:27017';

let shardConnections = {}

const connectShards = async () => {
  const client1 = new MongoClient(mongoUri)
  const client2 = new MongoClient(mongoUri)

  await client1.connect();
  await client2.connect();

  shardConnections = {
    shard1: client1.db('shard1'),
    shard2: client2.db('shard2')
  }

  console.log('Connected to shards');
}

const getShard = (userId) => {
  return userId % 2 === 0 ? shardConnections.shard1 : shardConnections.shard2;
}

module.exports = {
  connectShards,
  getShard
}